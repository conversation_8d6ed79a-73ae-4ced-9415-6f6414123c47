package com.hydee.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorResourceFactory;

/**
 * <AUTHOR>
 * @Description ReactorNetty
 * @Date 2025/03/27/16:48
 */
@Configuration
public class ReactorNettyConfig {

    /**
     * @return
     * @See reactor.netty.tcp.TcpResources#create
     */
    @Bean
    public ReactorResourceFactory reactorClientResourceFactory() {
        /**
         * 定义 Netty 的Selector 线程数，负责监听网络事件（如连接建立、数据到达）
         * 默认值：-1（由 Netty 自动分配，通常为 CPU 核心数）。
         * 若为 CPU 密集型应用，设置为 1 可减少上下文切换；若为高并发 I/O 密集型应用，保留默认值或按需调整。
         */
        System.setProperty("reactor.netty.ioSelectCount", "1");
        /**
         * 定义 Netty 的I/O 工作线程数，负责处理网络读写、编解码等任务。
         * ：通常设置为 CPU 核心数 * 2（需与容器 CPU 资源限制对齐）。
         */
        System.setProperty("reactor.netty.ioWorkerCount",
            String.valueOf(Runtime.getRuntime().availableProcessors() * 2));
        return new ReactorResourceFactory();
    }
}
