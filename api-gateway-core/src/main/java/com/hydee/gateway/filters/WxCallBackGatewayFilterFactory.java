package com.hydee.gateway.filters;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.honey.common.vo.ResultVO;
import com.hydee.gateway.util.StringUtil;
import com.hydee.gateway.util.WxShaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

import static org.springframework.http.HttpMethod.GET;
import static org.springframework.http.HttpMethod.POST;

/**
 * 微信开放平台回调鉴权
 *
 * <AUTHOR>
 * @date 2024/12/24 17:16
 */

@Slf4j
@Component
public class WxCallBackGatewayFilterFactory extends BaseGatewayFilterFactory<Object> {

    @Value("${wx-auth.token:PnIoq8r39hcnqjg0}")
    private String token;

    @Override
    public GatewayFilter apply(Object config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            ServerHttpResponse response = exchange.getResponse();
            List<String> signature = request.getQueryParams().get("signature");
            List<String> timestamp = request.getQueryParams().get("timestamp");
            List<String> nonce = request.getQueryParams().get("nonce");
            log.info("微信回调请求参数: {}", JSONObject.toJSONString(request.getQueryParams()));
            if (CollectionUtils.isNotEmpty(signature) && CollectionUtils.isNotEmpty(timestamp) && CollectionUtils.isNotEmpty(nonce)){
                String sha1 = WxShaUtil.getSHA1(token, timestamp.get(0), nonce.get(0));
                if (sha1.equalsIgnoreCase(signature.get(0))) {
                    if (GET.equals(request.getMethod())) {
                        //微信验证请求，直接返回回文
                        List<String> echostr = request.getQueryParams().get("echostr");
                        response.setStatusCode(HttpStatus.OK);
                        DataBuffer dataBuffer = response.bufferFactory().
                                wrap(echostr.get(0).getBytes());
                        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                        return response.writeWith(Flux.just(dataBuffer));
                    }else if (POST.equals(request.getMethod())) {
                        return chain.filter(exchange);
                    }else {
                        log.error("微信回调请求方法错误, param: {}", JSONObject.toJSONString(request.getQueryParams()));
                        response.setStatusCode(HttpStatus.BAD_REQUEST);
                        DataBuffer dataBuffer = response.bufferFactory().
                                wrap(StringUtil.convert2Bytes(ResultVO.fail(HttpStatus.BAD_REQUEST, "微信回调请求方法错误")));
                        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                        return response.writeWith(Flux.just(dataBuffer));
                    }
                } else {
                    log.error("微信回调签名验证失败, param: {}", JSONObject.toJSONString(request.getQueryParams()));
                    response.setStatusCode(HttpStatus.UNAUTHORIZED);
                    DataBuffer dataBuffer = response.bufferFactory().
                            wrap(StringUtil.convert2Bytes(ResultVO.fail(HttpStatus.UNAUTHORIZED, "微信回调签名验证失败")));
                    response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                    return response.writeWith(Flux.just(dataBuffer));
                }
            }else {
                log.error("微信回调请求参数缺失, param: {}", JSONObject.toJSONString(request.getQueryParams()));
                response.setStatusCode(HttpStatus.BAD_REQUEST);
                DataBuffer dataBuffer = response.bufferFactory().
                        wrap(StringUtil.convert2Bytes(ResultVO.fail(HttpStatus.BAD_REQUEST, "微信回调请求参数缺失")));
                response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                return response.writeWith(Flux.just(dataBuffer));
            }
        };
    }
}
