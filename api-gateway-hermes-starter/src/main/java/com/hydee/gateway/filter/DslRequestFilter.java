package com.hydee.gateway.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hydee.gateway.DslRouteConfig;
import com.hydee.gateway.utils.DslEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Since: 2025/07/18 11:14
 * Author: qs
 */
@Component
public class DslRequestFilter implements GlobalFilter, Ordered {

//    @Autowired
//    private DslConfigService dslConfigService;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String path = exchange.getRequest().getPath().value();

//        DslRouteConfig config = dslConfigService.getByPath(path);
//        if (config == null || config.getRequest() == null) {
//            return chain.filter(exchange);
//        }
        String conStr = "{\n" +
                "  \"request\": {\n" +
                "    \"mapping\": {\n" +
                "      \"targetField1\": \"source.paramA\",\n" +
                "      \"targetField2\": \"source.paramB\",\n" +
                "      \"userId\": \"header.X-USER-ID\"\n" +
                "    },\n" +
                "    \"filters\": [\"paramA\", \"paramB\", \"X-USER-ID\"], \n" +
                "    \"headerMapping\": {\n" +
                "      \"Authorization\": \"source.token\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"response\": {\n" +
                "    \"mapping\": {\n" +
                "      \"data.name\": \"result.username\",\n" +
                "      \"data.age\": \"result.userAge\"\n" +
                "    },\n" +
                "    \"filters\": [\"result.username\", \"result.userAge\", \"result.account\"],\n" +
                "    \"masking\": {\n" +
                "      \"result.phone\": \"MOBILE\",\n" +
                "      \"result.idCard\": \"ID_CARD\"\n" +
                "    },\n" +
                "    \"custom\": {\n" +
                "      \"statusText\": \"result.code == 200 ? '成功' : '失败'\"\n" +
                "    }\n" +
                "  }\n" +
                "}\n";
        DslRouteConfig config = JSON.parseObject(conStr, DslRouteConfig.class);
        config.setPath("/c/demo/getById");

        ServerHttpRequest request = exchange.getRequest();
        return DataBufferUtils.join(request.getBody()).flatMap(dataBuffer -> {
            byte[] bodyBytes = new byte[dataBuffer.readableByteCount()];
            dataBuffer.read(bodyBytes);
            DataBufferUtils.release(dataBuffer);
            String bodyStr = new String(bodyBytes, StandardCharsets.UTF_8);

            Map<String, Object> newBodyMap = DslEngine.executeRequest(config.getRequest(), bodyStr, request.getHeaders());
            byte[] newBodyBytes = JSONObject.toJSONString(newBodyMap).getBytes(StandardCharsets.UTF_8);

            ServerHttpRequest mutatedRequest = exchange.getRequest().mutate()
                    .header("paramTest", "applied")
                    .build();

            ServerHttpRequestDecorator requestDecorator = new ServerHttpRequestDecorator(mutatedRequest) {
                @Override
                public Flux<DataBuffer> getBody() {
                    DataBufferFactory bufferFactory = new DefaultDataBufferFactory();
                    return Flux.just(bufferFactory.wrap(newBodyBytes));
                }
            };

            return chain.filter(exchange.mutate().request(requestDecorator).build());
        });
    }

    @Override
    public int getOrder() {
        return -100;
    }
}
