package com.hydee.gateway.filter;

import com.hydee.gateway.DslRouteConfig;
import com.hydee.gateway.utils.DslEngine;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * Since: 2025/07/18 11:15
 * Author: qs
 */
//@Component
public class DslResponseFilter implements GlobalFilter, Ordered {

//    @Autowired
//    private DslConfigService dslConfigService;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String path = exchange.getRequest().getPath().value();
//        DslRouteConfig config = dslConfigService.getByPath(path);
//        if (config == null || config.getResponse() == null) {
//            return chain.filter(exchange);
//        }
        DslRouteConfig config = new DslRouteConfig();


        ServerHttpResponse originalResponse = exchange.getResponse();
        DataBufferFactory bufferFactory = originalResponse.bufferFactory();

        ServerHttpResponseDecorator decoratedResponse = new ServerHttpResponseDecorator(originalResponse) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                Flux<? extends DataBuffer> fluxBody = Flux.from(body);

                return super.writeWith(
                        fluxBody.map(dataBuffer -> {
                            byte[] content = new byte[dataBuffer.readableByteCount()];
                            dataBuffer.read(content);
                            DataBufferUtils.release(dataBuffer);
                            String rawBody = new String(content, StandardCharsets.UTF_8);

                            String newBody = DslEngine.executeResponse(config.getResponse(), rawBody);
                            byte[] newBodyBytes = newBody.getBytes(StandardCharsets.UTF_8);
                            return bufferFactory.wrap(newBodyBytes);
                        })
                );
            }
        };

        return chain.filter(exchange.mutate().response(decoratedResponse).build());
    }

    @Override
    public int getOrder() {
        return 100;
    }
}

